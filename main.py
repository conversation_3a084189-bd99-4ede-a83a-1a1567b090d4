#!/usr/bin/env python3
"""
Prisma OCR & Translator - 本地PDF与图像OCR及翻译工具
主程序入口点 (基于pywebview)

Author: AI Assistant
Date: 2025-09-11
Version: 2.0.0
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from webui.web_controller import WebController


def setup_logging():
    """设置日志系统"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "prisma_ocr.log", encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def check_dependencies():
    """检查必要的依赖是否安装"""
    missing_deps = []

    try:
        import webview
    except ImportError:
        missing_deps.append("pywebview")

    try:
        import paddle
    except ImportError:
        missing_deps.append("paddlepaddle")

    try:
        import paddleocr
    except ImportError:
        missing_deps.append("paddleocr")

    if missing_deps:
        print("错误: 缺少以下依赖包:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False

    return True


def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("启动 Prisma OCR & Translator (WebView版本)")

    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    try:
        # 创建Web控制器
        debug_mode = "--debug" in sys.argv
        web_controller = WebController(debug=debug_mode)

        logger.info("Web应用程序启动成功")

        # 启动Web应用程序
        web_controller.start_application()

    except Exception as e:
        logger.error(f"应用程序启动失败: {e}")
        print(f"启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
