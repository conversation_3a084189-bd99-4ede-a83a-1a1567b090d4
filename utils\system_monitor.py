"""
系统资源监控工具
"""

import psutil
import time
import threading
from typing import Dict, Optional, Callable

try:
    import pynvml
    NVIDIA_GPU_AVAILABLE = True
except ImportError:
    NVIDIA_GPU_AVAILABLE = False


class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self, update_interval: float = 1.0):
        """
        初始化系统监控器

        Args:
            update_interval: 更新间隔（秒）
        """
        self.update_interval = update_interval
        self.is_monitoring = False
        self.monitor_thread = None

        # 回调函数
        self.resource_callback: Optional[Callable] = None
        
        # GPU监控初始化
        self.gpu_available = False
        self.gpu_handle = None
        
        if NVIDIA_GPU_AVAILABLE:
            try:
                pynvml.nvmlInit()
                device_count = pynvml.nvmlDeviceGetCount()
                if device_count > 0:
                    self.gpu_handle = pynvml.nvmlDeviceGetHandleByIndex(0)  # 使用第一个GPU
                    self.gpu_available = True
            except Exception as e:
                print(f"GPU监控初始化失败: {e}")
                self.gpu_available = False
    
    def start_monitoring(self) -> None:
        """开始监控"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()

    def stop_monitoring(self) -> None:
        """停止监控"""
        if self.is_monitoring:
            self.is_monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=2.0)

    def _monitor_loop(self) -> None:
        """监控循环"""
        while self.is_monitoring:
            try:
                resources = self.get_system_resources()
                if self.resource_callback:
                    self.resource_callback(resources)
            except Exception as e:
                print(f"更新系统资源信息失败: {e}")

            time.sleep(self.update_interval)
    
    def get_system_resources(self) -> Dict[str, float]:
        """
        获取系统资源使用情况
        
        Returns:
            包含CPU、内存、GPU使用率的字典
        """
        resources = {}
        
        # CPU使用率
        try:
            resources['cpu_percent'] = psutil.cpu_percent(interval=None)
        except Exception as e:
            print(f"获取CPU使用率失败: {e}")
            resources['cpu_percent'] = 0.0
        
        # 内存使用率
        try:
            memory = psutil.virtual_memory()
            resources['memory_percent'] = memory.percent
            resources['memory_used_gb'] = memory.used / (1024**3)
            resources['memory_total_gb'] = memory.total / (1024**3)
        except Exception as e:
            print(f"获取内存使用率失败: {e}")
            resources['memory_percent'] = 0.0
            resources['memory_used_gb'] = 0.0
            resources['memory_total_gb'] = 0.0
        
        # GPU使用率
        if self.gpu_available and self.gpu_handle:
            try:
                # GPU使用率
                utilization = pynvml.nvmlDeviceGetUtilizationRates(self.gpu_handle)
                resources['gpu_percent'] = utilization.gpu
                
                # GPU内存使用情况
                memory_info = pynvml.nvmlDeviceGetMemoryInfo(self.gpu_handle)
                resources['gpu_memory_percent'] = (memory_info.used / memory_info.total) * 100
                resources['gpu_memory_used_gb'] = memory_info.used / (1024**3)
                resources['gpu_memory_total_gb'] = memory_info.total / (1024**3)
                
                # GPU温度
                try:
                    temperature = pynvml.nvmlDeviceGetTemperature(self.gpu_handle, pynvml.NVML_TEMPERATURE_GPU)
                    resources['gpu_temperature'] = temperature
                except:
                    resources['gpu_temperature'] = 0
                
            except Exception as e:
                print(f"获取GPU使用率失败: {e}")
                resources['gpu_percent'] = 0.0
                resources['gpu_memory_percent'] = 0.0
                resources['gpu_memory_used_gb'] = 0.0
                resources['gpu_memory_total_gb'] = 0.0
                resources['gpu_temperature'] = 0
        else:
            resources['gpu_percent'] = 0.0
            resources['gpu_memory_percent'] = 0.0
            resources['gpu_memory_used_gb'] = 0.0
            resources['gpu_memory_total_gb'] = 0.0
            resources['gpu_temperature'] = 0
        
        return resources
    
    def get_cpu_count(self) -> int:
        """获取CPU核心数"""
        return psutil.cpu_count()
    
    def get_memory_info(self) -> Dict[str, float]:
        """获取详细内存信息"""
        memory = psutil.virtual_memory()
        return {
            'total': memory.total / (1024**3),
            'available': memory.available / (1024**3),
            'used': memory.used / (1024**3),
            'percent': memory.percent
        }
    
    def get_disk_usage(self, path: str = '/') -> Dict[str, float]:
        """获取磁盘使用情况"""
        try:
            disk = psutil.disk_usage(path)
            return {
                'total': disk.total / (1024**3),
                'used': disk.used / (1024**3),
                'free': disk.free / (1024**3),
                'percent': (disk.used / disk.total) * 100
            }
        except Exception as e:
            print(f"获取磁盘使用情况失败: {e}")
            return {'total': 0, 'used': 0, 'free': 0, 'percent': 0}
    
    def get_gpu_info(self) -> Optional[Dict[str, str]]:
        """获取GPU信息"""
        if not self.gpu_available or not self.gpu_handle:
            return None
        
        try:
            name = pynvml.nvmlDeviceGetName(self.gpu_handle).decode('utf-8')
            driver_version = pynvml.nvmlSystemGetDriverVersion().decode('utf-8')
            
            return {
                'name': name,
                'driver_version': driver_version,
                'available': True
            }
        except Exception as e:
            print(f"获取GPU信息失败: {e}")
            return None
    
    def is_gpu_available(self) -> bool:
        """检查GPU是否可用"""
        return self.gpu_available
    
    def __del__(self):
        """析构函数"""
        self.stop_monitoring()
        if NVIDIA_GPU_AVAILABLE and self.gpu_available:
            try:
                pynvml.nvmlShutdown()
            except:
                pass
