"""
图像处理工具函数
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance
from pathlib import Path
from typing import Union, Tuple, Optional, List
import pdf2image


class ImageUtils:
    """图像处理工具类"""
    
    @classmethod
    def load_image(cls, image_path: Union[str, Path]) -> np.ndarray:
        """
        加载图像
        
        Args:
            image_path: 图像路径
            
        Returns:
            图像数组 (BGR格式)
        """
        image_path = str(image_path)
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法加载图像: {image_path}")
        return image
    
    @classmethod
    def load_image_pil(cls, image_path: Union[str, Path]) -> Image.Image:
        """
        使用PIL加载图像
        
        Args:
            image_path: 图像路径
            
        Returns:
            PIL图像对象
        """
        return Image.open(image_path)
    
    @classmethod
    def save_image(cls, image: np.ndarray, output_path: Union[str, Path]) -> None:
        """
        保存图像
        
        Args:
            image: 图像数组
            output_path: 输出路径
        """
        cv2.imwrite(str(output_path), image)
    
    @classmethod
    def resize_image(cls, image: np.ndarray, 
                    width: Optional[int] = None, 
                    height: Optional[int] = None,
                    keep_aspect_ratio: bool = True) -> np.ndarray:
        """
        调整图像大小
        
        Args:
            image: 输入图像
            width: 目标宽度
            height: 目标高度
            keep_aspect_ratio: 是否保持宽高比
            
        Returns:
            调整后的图像
        """
        h, w = image.shape[:2]
        
        if width is None and height is None:
            return image
        
        if keep_aspect_ratio:
            if width is not None and height is not None:
                # 计算缩放比例，保持宽高比
                scale_w = width / w
                scale_h = height / h
                scale = min(scale_w, scale_h)
                new_width = int(w * scale)
                new_height = int(h * scale)
            elif width is not None:
                scale = width / w
                new_width = width
                new_height = int(h * scale)
            else:  # height is not None
                scale = height / h
                new_width = int(w * scale)
                new_height = height
        else:
            new_width = width or w
            new_height = height or h
        
        return cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
    
    @classmethod
    def enhance_image_for_ocr(cls, image: np.ndarray) -> np.ndarray:
        """
        为OCR优化图像
        
        Args:
            image: 输入图像
            
        Returns:
            优化后的图像
        """
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # 去噪
        denoised = cv2.medianBlur(gray, 3)
        
        # 自适应阈值处理
        binary = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # 形态学操作，去除小噪点
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    @classmethod
    def convert_pdf_to_images(cls, pdf_path: Union[str, Path],
                            dpi: int = 200,
                            first_page: Optional[int] = None,
                            last_page: Optional[int] = None) -> List[Image.Image]:
        """
        将PDF转换为图像列表

        Args:
            pdf_path: PDF文件路径
            dpi: 分辨率
            first_page: 起始页码（从1开始）
            last_page: 结束页码

        Returns:
            图像列表
        """
        # 首先尝试使用PyMuPDF（不需要外部依赖）
        try:
            return cls._convert_pdf_with_pymupdf(pdf_path, dpi, first_page, last_page)
        except ImportError:
            pass  # PyMuPDF未安装，尝试pdf2image
        except Exception as e:
            print(f"PyMuPDF转换失败，尝试pdf2image: {e}")

        # 回退到pdf2image
        try:
            images = pdf2image.convert_from_path(
                pdf_path,
                dpi=dpi,
                first_page=first_page,
                last_page=last_page,
                fmt='RGB'
            )
            return images
        except Exception as e:
            raise ValueError(f"PDF转换失败: {e}")

    @classmethod
    def _convert_pdf_with_pymupdf(cls, pdf_path: Union[str, Path],
                                 dpi: int = 200,
                                 first_page: Optional[int] = None,
                                 last_page: Optional[int] = None) -> List[Image.Image]:
        """
        使用PyMuPDF转换PDF（不需要外部依赖）
        """
        try:
            import fitz  # PyMuPDF
            import io
        except ImportError:
            raise ImportError("PyMuPDF未安装，请运行: pip install PyMuPDF")

        doc = fitz.open(str(pdf_path))
        images = []

        # 确定页面范围
        total_pages = doc.page_count
        start_page = (first_page - 1) if first_page else 0
        end_page = (last_page - 1) if last_page else (total_pages - 1)

        # 限制页面范围
        start_page = max(0, min(start_page, total_pages - 1))
        end_page = max(start_page, min(end_page, total_pages - 1))

        try:
            for page_num in range(start_page, end_page + 1):
                page = doc[page_num]

                # 设置缩放因子（DPI转换）
                zoom = dpi / 72.0  # 72是PDF的默认DPI
                mat = fitz.Matrix(zoom, zoom)

                # 渲染页面为图像
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")

                # 转换为PIL Image
                img = Image.open(io.BytesIO(img_data))
                images.append(img)

                # 清理内存
                pix = None
        finally:
            doc.close()

        return images
    
    @classmethod
    def pil_to_cv2(cls, pil_image: Image.Image) -> np.ndarray:
        """
        将PIL图像转换为OpenCV格式
        
        Args:
            pil_image: PIL图像
            
        Returns:
            OpenCV图像 (BGR格式)
        """
        # 转换为RGB（如果不是的话）
        if pil_image.mode != 'RGB':
            pil_image = pil_image.convert('RGB')
        
        # 转换为numpy数组
        cv2_image = np.array(pil_image)
        
        # RGB转BGR
        cv2_image = cv2.cvtColor(cv2_image, cv2.COLOR_RGB2BGR)
        
        return cv2_image
    
    @classmethod
    def cv2_to_pil(cls, cv2_image: np.ndarray) -> Image.Image:
        """
        将OpenCV图像转换为PIL格式
        
        Args:
            cv2_image: OpenCV图像 (BGR格式)
            
        Returns:
            PIL图像
        """
        # BGR转RGB
        rgb_image = cv2.cvtColor(cv2_image, cv2.COLOR_BGR2RGB)
        
        # 转换为PIL图像
        pil_image = Image.fromarray(rgb_image)
        
        return pil_image
    
    @classmethod
    def rotate_image(cls, image: np.ndarray, angle: float) -> np.ndarray:
        """
        旋转图像
        
        Args:
            image: 输入图像
            angle: 旋转角度（度）
            
        Returns:
            旋转后的图像
        """
        h, w = image.shape[:2]
        center = (w // 2, h // 2)
        
        # 计算旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        
        # 计算新的边界框大小
        cos_val = abs(rotation_matrix[0, 0])
        sin_val = abs(rotation_matrix[0, 1])
        new_w = int((h * sin_val) + (w * cos_val))
        new_h = int((h * cos_val) + (w * sin_val))
        
        # 调整旋转矩阵的平移部分
        rotation_matrix[0, 2] += (new_w / 2) - center[0]
        rotation_matrix[1, 2] += (new_h / 2) - center[1]
        
        # 执行旋转
        rotated = cv2.warpAffine(image, rotation_matrix, (new_w, new_h), 
                               flags=cv2.INTER_LINEAR, 
                               borderMode=cv2.BORDER_CONSTANT,
                               borderValue=(255, 255, 255))
        
        return rotated
    
    @classmethod
    def crop_image(cls, image: np.ndarray, 
                  x: int, y: int, width: int, height: int) -> np.ndarray:
        """
        裁剪图像
        
        Args:
            image: 输入图像
            x: 起始x坐标
            y: 起始y坐标
            width: 宽度
            height: 高度
            
        Returns:
            裁剪后的图像
        """
        return image[y:y+height, x:x+width]
    
    @classmethod
    def get_image_info(cls, image_path: Union[str, Path]) -> dict:
        """
        获取图像信息
        
        Args:
            image_path: 图像路径
            
        Returns:
            图像信息字典
        """
        try:
            with Image.open(image_path) as img:
                return {
                    'width': img.width,
                    'height': img.height,
                    'mode': img.mode,
                    'format': img.format,
                    'size_bytes': Path(image_path).stat().st_size
                }
        except Exception as e:
            raise ValueError(f"无法获取图像信息: {e}")
    
    @classmethod
    def enhance_contrast(cls, image: np.ndarray, factor: float = 1.5) -> np.ndarray:
        """
        增强图像对比度
        
        Args:
            image: 输入图像
            factor: 对比度因子
            
        Returns:
            增强后的图像
        """
        # 转换为PIL图像
        pil_image = cls.cv2_to_pil(image)
        
        # 增强对比度
        enhancer = ImageEnhance.Contrast(pil_image)
        enhanced = enhancer.enhance(factor)
        
        # 转换回OpenCV格式
        return cls.pil_to_cv2(enhanced)
